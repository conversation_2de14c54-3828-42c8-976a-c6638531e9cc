using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Tabula.PWG.MobileController.CLI
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var config = ParseCommandLineArgs(args);

            Console.WriteLine("=== S-ARGAME Mobile Controller Network Tester ===");
            Console.WriteLine($"Configuration:");
            Console.WriteLine($"  Time: {config.Time}s");
            Console.WriteLine($"  Start Delay: {config.StartDelay}s");
            Console.WriteLine($"  End Delay: {config.EndDelay}s");
            Console.WriteLine($"  Use P2P: {config.UseP2P}");
            Console.WriteLine($"  Auth Code: {config.AuthCode}");
            Console.WriteLine($"  Server: {config.ServerAddress}:{config.ServerPort}");
            Console.WriteLine();

            var simulator = new PlayerSimulator(config);

            try
            {
                await simulator.StartSimulationAsync();

                Console.WriteLine($"Simulation completed successfully.");
                await Task.Delay(TimeSpan.FromSeconds(config.EndDelay));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Simulation failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static SimulationConfig ParseCommandLineArgs(string[] args)
        {
            var config = new SimulationConfig();

            for (int i = 0; i < args.Length; i++)
            {
                switch (args[i].ToLower())
                {
                    case "--time":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float time))
                            config.Time = time;
                        i++;
                        break;
                    case "--start-delay":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float startDelay))
                            config.StartDelay = startDelay;
                        i++;
                        break;
                    case "--end-delay":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float endDelay))
                            config.EndDelay = endDelay;
                        i++;
                        break;
                    case "--use-p2p":
                        if (i + 1 < args.Length && bool.TryParse(args[i + 1], out bool useP2P))
                            config.UseP2P = useP2P;
                        i++;
                        break;
                    case "--auth-code":
                        if (i + 1 < args.Length)
                            config.AuthCode = args[i + 1];
                        i++;
                        break;
                    case "--server-address":
                        if (i + 1 < args.Length)
                            config.ServerAddress = args[i + 1];
                        i++;
                        break;
                    case "--server-port":
                        if (i + 1 < args.Length && int.TryParse(args[i + 1], out int port))
                            config.ServerPort = port;
                        i++;
                        break;
                }
            }

            return config;
        }
    }

    public class SimulationConfig
    {
        public float Time { get; set; } = 60f;
        public float StartDelay { get; set; } = 2f;
        public float EndDelay { get; set; } = 5f;
        public bool UseP2P { get; set; } = true;
        public string AuthCode { get; set; } = "";
        public string ServerAddress { get; set; } = "127.0.0.1";
        public int ServerPort { get; set; } = 16100;
    }

    // Unity Vector2 replacement
    [Serializable]
    public struct Vector2
    {
        public float x, y;

        public Vector2(float x, float y)
        {
            this.x = x;
            this.y = y;
        }

        public static Vector2 Zero => new Vector2(0, 0);
    }

    // Data structures from Unity ControllerData.cs
    [Serializable]
    public struct StickData
    {
        public Vector2 direction;
        public bool is_dirty;

        public void SetData(Vector2 v)
        {
            is_dirty = true;
            direction.x = v.x;
            direction.y = v.y;
        }
    }

    [Serializable]
    public struct ButtonData
    {
        public bool pressed;
        public bool is_dirty;

        public void SetData(bool b)
        {
            is_dirty = false;
            if (pressed != b)
            {
                pressed = b;
                is_dirty = true;
            }
        }
    }

    [Serializable]
    public struct TriggerData
    {
        public float value;
        public void SetData(float v) => value = v;
    }

    [Serializable]
    public struct TouchData
    {
        public bool pressed;
        public float x;
        public float y;
        public bool is_dirty;

        public void SetData(bool pressed, float x, float y)
        {
            this.pressed = pressed;
            this.x = x;
            this.y = y;
            is_dirty = true;
        }
    }

    [Serializable]
    public struct GyroData
    {
        public float heading;
        public float pitch;
        public float roll;

        public void SetData(float heading, float pitch, float roll)
        {
            this.heading = heading;
            this.pitch = pitch;
            this.roll = roll;
        }
    }

    public class ControllerData
    {
        public StickData[] Sticks = new StickData[2];
        public ButtonData[] Buttons = new ButtonData[11];
        public TriggerData[] Triggers = new TriggerData[2];
        public TouchData Touch;
        public GyroData Gyro;

        public bool IsDirty =>
            Sticks[0].is_dirty ||
            Buttons[0].is_dirty || Buttons[1].is_dirty || Buttons[2].is_dirty || Buttons[3].is_dirty ||
            Buttons[8].is_dirty || Buttons[9].is_dirty ||
            Touch.is_dirty;

        public void ResetDirty()
        {
            Sticks[0].is_dirty = false;
            for (int i = 0; i < Buttons.Length; i++)
                Buttons[i].is_dirty = false;
            Touch.is_dirty = false;
        }
    }

    // Simulated data structures
    [Serializable]
    public class SimulatedAtom
    {
        public float time;
        public ControllerData data = new ControllerData();
    }

    public class SimulatedData
    {
        public List<SimulatedAtom> items = new List<SimulatedAtom>();

        // Create default simulation data
        public static SimulatedData CreateDefault()
        {
            var data = new SimulatedData();

            // Add some basic simulated input patterns
            for (int i = 0; i < 10; i++)
            {
                var atom = new SimulatedAtom
                {
                    time = 1.0f, // 1 second intervals
                    data = new ControllerData()
                };

                // Simulate some stick movement
                atom.data.Sticks[0].SetData(new Vector2(
                    (float)Math.Sin(i * 0.5) * 0.5f,
                    (float)Math.Cos(i * 0.5) * 0.5f
                ));

                // Simulate button presses
                if (i % 3 == 0)
                    atom.data.Buttons[0].SetData(true);

                data.items.Add(atom);
            }

            return data;
        }
    }

    // Player Controller Message structures (from Unity)
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct PlayerControllerMessage
    {
        public const int Version = 13;
        public const int KCPMSG_PLAYERCONTROLLER_DATA = 150;
        public const int KCPMSG_PLAYERCONTROLLER_COMMAND = 151;
        public const int KCPMSG_PLAYERCONTROLLER_PING = 160;

        // Flags
        public const int Flags_GamePad = 1 << 0;
        public const int Flags_Touch = 1 << 1;
        public const int Flags_Default = Flags_GamePad;

        public byte version;
        public int player_id;
        public ulong seq;
        public uint flags;

        // Gamepad
        public float stick1_x, stick1_y;
        public bool stick1_button;
        public float stick2_x, stick2_y;
        public bool stick2_button;
        public int dpad;

        // Touch
        public float touch_x, touch_y;
        public bool touch_pressed;

        // Gyro
        public float gyro_heading, gyro_pitch, gyro_roll;

        public byte[] ToByteArray()
        {
            int size = Marshal.SizeOf(this);
            byte[] arr = new byte[size];
            IntPtr ptr = Marshal.AllocHGlobal(size);
            try
            {
                Marshal.StructureToPtr(this, ptr, true);
                Marshal.Copy(ptr, arr, 0, size);
            }
            finally
            {
                Marshal.FreeHGlobal(ptr);
            }
            return arr;
        }
    }

    // Server message structure
    [Serializable]
    public class PlayerControllerServerMessage
    {
        public const string SERVER_PLAYER_STATUS = "SERVER_PLAYER_STATUS";
        public const string SERVER_ASK_DISCONNECT = "SERVER_ASK_DISCONNECT";
        public const string SERVER_REPORT_KILL = "SERVER_REPORT_KILL";

        public string command = "";
        public string package_id = "";
        public string module_id = "";
        public List<string> license_features = new List<string>();
        public string player_name = "";
        public string player_avatar = "";
        public int player_avatar_index = 0;
        public int player_color = 0;
        public float player_health = 0;
        public int player_lives = 0;
        public float position_x, position_y;
        public int player_status;
        public int wait_queue = 0;

        public bool Equals(PlayerControllerServerMessage other)
        {
            if (other == null) return false;
            return command == other.command &&
                   player_name == other.player_name &&
                   player_status == other.player_status &&
                   wait_queue == other.wait_queue &&
                   player_lives == other.player_lives &&
                   player_health == other.player_health;
        }
    }

    // Player join/leave result structures
    public class PlayerJoinResult
    {
        public int player_client_id;
        public int reason;
        public int player_flags;
    }

    public class PlayerLeftResult
    {
        public const int ReasonLeft_ClientLeft = 1;
        public const int ReasonLeft_DenyClientNotFound = -1;
        public const int ReasonLeft_ClientDisconnected = -2;
        public const int ReasonLeft_KeepAliveExpired = -3;

        public int reason;

        public PlayerLeftResult(int reason)
        {
            this.reason = reason;
        }
    }

    // Task result wrapper (replacement for CoroutineWaitForTaskResult)
    public class TaskResult
    {
        public bool HasTimedOut { get; set; }
        public bool TaskIsFaulted { get; set; }
        public bool TaskIsCanceled { get; set; }
        public Exception? Exception { get; set; }
    }

    // Simplified UDP client for networking
    public class SimpleUdpClient : IDisposable
    {
        private UdpClient? udpClient;
        private IPEndPoint? remoteEndPoint;
        private bool isConnected;
        private CancellationTokenSource? cancellationTokenSource;

        public bool IsConnected => isConnected;
        public Action<byte[]>? OnMessageReceived;

        public async Task<bool> ConnectAsync(string address, int port, float timeoutSeconds, CancellationToken cancellationToken = default)
        {
            try
            {
                remoteEndPoint = new IPEndPoint(IPAddress.Parse(address), port);
                udpClient = new UdpClient();

                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(TimeSpan.FromSeconds(timeoutSeconds));

                // UdpClient.Connect is synchronous, so we wrap it in Task.Run for timeout handling
                await Task.Run(() => udpClient.Connect(remoteEndPoint), timeoutCts.Token);
                isConnected = true;

                // Start receiving messages
                cancellationTokenSource = new CancellationTokenSource();
                _ = Task.Run(() => ReceiveLoop(cancellationTokenSource.Token), cancellationToken);

                Console.WriteLine($"PMClient: Connected to {address}:{port}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Connection failed: {ex.Message}");
                return false;
            }
        }

        private async Task ReceiveLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isConnected)
                {
                    var result = await udpClient.ReceiveAsync();
                    OnMessageReceived?.Invoke(result.Buffer);
                }
            }
            catch (Exception ex)
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    Console.WriteLine($"PMClient: Receive error: {ex.Message}");
                }
            }
        }

        public async Task SendAsync(byte[] data)
        {
            if (isConnected && udpClient != null)
            {
                try
                {
                    await udpClient.SendAsync(data, data.Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"PMClient: Send error: {ex.Message}");
                }
            }
        }

        public void Disconnect()
        {
            isConnected = false;
            cancellationTokenSource?.Cancel();
            udpClient?.Close();
            udpClient?.Dispose();
            Console.WriteLine("PMClient: Disconnected");
        }

        public void Dispose()
        {
            Disconnect();
            cancellationTokenSource?.Dispose();
        }
    }

    // Main PlayerSimulator class (converted from Unity MonoBehaviour)
    public class PlayerSimulator
    {
        private readonly SimulationConfig config;
        private readonly SimpleUdpClient pmClient;
        private readonly SimulatedData simData;
        private PlayerControllerMessage dataMessage;
        private PlayerJoinResult? playerJoinResult;
        private PlayerControllerServerMessage? lastPlayerStatus;
        private Stopwatch? playerStatusTimer;
        private readonly Stopwatch simulationTimer;
        private bool serverRequestedDisconnect;
        private bool stopRequested;
        private bool isConnected;

        // Configuration from Unity PlayerSimulator
        public string PLAYER_GUID { get; private set; } = "";
        public string PLAYER_NAME { get; private set; } = "";
        public int PLAYER_ID { get; private set; } = -1;
        public string AUTH_CODE => config.AuthCode;
        public string SERVER_ADDRESS => config.ServerAddress;
        public int SERVER_PORT => config.ServerPort;
        public bool UseP2P => config.UseP2P;

        // Timing and networking parameters (from Unity)
        public float CheckAuthCodeTimeout = 60;
        public float P2PConnectionTimeout = 10;
        public float ClientConnectTimeout = 10;
        public float KeepAliveInterval = 0.5f;
        public float SendDataFrequency = 30f;
        public float PingInterval = 1.0f;
        public int ServerTimeout = 5000;

        // Statistics
        public long SentPackets { get; private set; }
        public long ReceivedPackets { get; private set; }

        public PlayerSimulator(SimulationConfig config)
        {
            this.config = config;
            this.pmClient = new SimpleUdpClient();
            this.simData = SimulatedData.CreateDefault();
            this.dataMessage = new PlayerControllerMessage();
            this.simulationTimer = new Stopwatch();

            // Setup message handling
            pmClient.OnMessageReceived = OnServerMessage;
        }

        public async Task StartSimulationAsync(CancellationToken cancellationToken = default)
        {
            SetNameData("Starting simulation");

            await Task.Delay(TimeSpan.FromSeconds(config.StartDelay), cancellationToken);

            PLAYER_GUID = Guid.NewGuid().ToString();

            if (UseP2P)
            {
                Console.WriteLine($"StartClientSimulation [P2P] AuthCode:{AUTH_CODE} Guid:{PLAYER_GUID}");
                SetNameData($"P2P: start");

                // P2P is excluded as per requirements
                throw new NotImplementedException("P2P networking is not implemented in console version. Use --use-p2p false for direct connection.");
            }
            else
            {
                Console.WriteLine($"StartClientSimulation [direct] Guid:{PLAYER_GUID}");
            }

            playerStatusTimer = null;

            // Connect to server
            var connectResult = await pmClient.ConnectAsync(SERVER_ADDRESS, SERVER_PORT, ClientConnectTimeout, cancellationToken);

            if (!connectResult)
            {
                SetNameData($"Connection failed");
                Console.WriteLine("PMClient: OnConnectionFailed()");
                return;
            }

            if (!pmClient.IsConnected)
            {
                SetNameData($"Connection failed");
                Console.WriteLine("PMClient: OnConnectionFailed()");
                return;
            }

            // Simulate player join (simplified - no actual RPC call)
            await SimulatePlayerJoinAsync(cancellationToken);

            if (playerJoinResult == null || playerJoinResult.reason < 0)
            {
                SetNameData($"Join error: reason={playerJoinResult?.reason ?? -1}");
                Console.WriteLine($"PMClient: cannot join, reason={playerJoinResult?.reason ?? -1}");
                return;
            }

            PLAYER_ID = playerJoinResult.player_client_id;
            Console.WriteLine($"PMClient: Join Successful, reason={playerJoinResult.reason} player_id={playerJoinResult.player_client_id} player_flags={playerJoinResult.player_flags}");

            isConnected = true;

            // Start sending simulated data
            if (simData != null)
            {
                await SimulateInputAsync(simData, config.Time, cancellationToken);
            }

            // Disconnect
            if (isConnected)
                await DisconnectAsync(cancellationToken);

            // Cleanup
            serverRequestedDisconnect = false;
            SentPackets = 0;
            ReceivedPackets = 0;
        }

        private async Task SimulatePlayerJoinAsync(CancellationToken cancellationToken)
        {
            // Simplified player join simulation
            await Task.Delay(100, cancellationToken); // Simulate network delay

            playerJoinResult = new PlayerJoinResult
            {
                player_client_id = new Random().Next(1000, 9999),
                reason = 1, // Success
                player_flags = 0
            };
        }

        private async Task SimulateInputAsync(SimulatedData data, float maxTime, CancellationToken cancellationToken)
        {
            dataMessage.player_id = PLAYER_ID;
            dataMessage.flags = PlayerControllerMessage.Flags_GamePad;

            simulationTimer.Start();
            var endTime = TimeSpan.FromSeconds(maxTime);
            var lastPingTime = DateTime.Now;

            Console.WriteLine($"PMClient: Starting input simulation for {maxTime} seconds");

            while (simulationTimer.Elapsed < endTime)
            {
                if (cancellationToken.IsCancellationRequested || stopRequested)
                {
                    SetNameData($"StopRequested");
                    break;
                }

                foreach (var d in data.items)
                {
                    if (simulationTimer.Elapsed >= endTime || cancellationToken.IsCancellationRequested)
                        break;

                    if (stopRequested)
                    {
                        SetNameData($"StopRequested");
                        return;
                    }

                    // Apply simulated data to message
                    if (d.data != null)
                    {
                        var stickData = d.data.Sticks[0];
                        dataMessage.stick1_x = stickData.direction.x;
                        dataMessage.stick1_y = stickData.direction.y;

                        var buttonData = d.data.Buttons[0];
                        if (buttonData.pressed)
                            dataMessage.flags |= PlayerControllerMessage.Flags_GamePad;

                        var touchData = d.data.Touch;
                        dataMessage.touch_x = touchData.x;
                        dataMessage.touch_y = touchData.y;
                        dataMessage.touch_pressed = touchData.pressed;
                    }

                    // Send message to server
                    await SendDataMessage();

                    // Wait for the specified time, but check for messages to send
                    var nextItemTime = DateTime.Now.AddSeconds(d.time);
                    while (DateTime.Now < nextItemTime)
                    {
                        if (simulationTimer.Elapsed >= endTime)
                            return;

                        // Send ping if needed
                        if ((DateTime.Now - lastPingTime).TotalSeconds > PingInterval)
                        {
                            await SendPing();
                            lastPingTime = DateTime.Now;
                        }

                        // Check for server disconnect request
                        if (serverRequestedDisconnect)
                        {
                            Console.WriteLine("PMClient: Disconnecting as server requested.");
                            await DisconnectAsync(cancellationToken, force: true);
                            SetNameData($"ServerRequestedDisconnect");
                            return;
                        }

                        // Check for server timeout
                        if (playerStatusTimer != null && playerStatusTimer.ElapsedMilliseconds > ServerTimeout)
                        {
                            Console.WriteLine("PMClient: Server timeout, no status received");
                            SetNameData($"ServerTimeout");
                            return;
                        }

                        await Task.Delay(50, cancellationToken); // Small delay to prevent busy waiting
                    }
                }
            }

            Console.WriteLine($"PMClient: Input simulation completed");
        }

        private async Task SendDataMessage()
        {
            try
            {
                dataMessage.version = PlayerControllerMessage.Version;
                dataMessage.seq++;

                var payload = dataMessage.ToByteArray();
                await pmClient.SendAsync(payload);
                SentPackets++;

                // Log every 100 packets to avoid spam
                if (SentPackets % 100 == 0)
                {
                    Console.WriteLine($"PMClient: Sent {SentPackets} packets");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Send error: {ex.Message}");
            }
        }

        private async Task SendPing()
        {
            try
            {
                // Simple ping message
                var pingData = Encoding.UTF8.GetBytes("PING");
                await pmClient.SendAsync(pingData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Ping error: {ex.Message}");
            }
        }

        private bool isDisconnecting = false;
        private async Task DisconnectAsync(CancellationToken cancellationToken = default, bool force = false)
        {
            if (isDisconnecting)
                return;

            isDisconnecting = true;

            // Notify server of player leave
            PlayerLeftResult res = null;

            if (force)
            {
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_KeepAliveExpired);
            }
            else if (!serverRequestedDisconnect)
            {
                // Simulate player leave call
                await Task.Delay(100, cancellationToken);
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_ClientLeft);
                Console.WriteLine($"PMClient: NetworkPlayerLeave reason={res.reason}");
            }
            else
            {
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_ClientDisconnected);
            }

            Console.WriteLine($"PMClient: Player id={PLAYER_ID} left server, reason={res.reason}");

            // Low-level client disconnect
            pmClient.Disconnect();

            PLAYER_ID = -1;
            PLAYER_GUID = string.Empty;
            isConnected = false;
            isDisconnecting = false;

            switch (res.reason)
            {
                case PlayerLeftResult.ReasonLeft_ClientLeft:
                    SetNameData($"ReasonLeft_ClientLeft");
                    break;

                case PlayerLeftResult.ReasonLeft_DenyClientNotFound:
                    SetNameData($"ReasonLeft_DenyClientNotFound");
                    break;

                case PlayerLeftResult.ReasonLeft_ClientDisconnected:
                    SetNameData($"ReasonLeft_ClientDisconnected");
                    break;
            }

            Console.WriteLine("PMClient: " + (res.reason > 0 ? "OnLeaveSuccessful()" : "OnLeaveFailed()"));
        }

        private void OnServerMessage(byte[] messageData)
        {
            ReceivedPackets++;

            try
            {
                // Try to parse as JSON server message
                var messageText = Encoding.UTF8.GetString(messageData);

                if (messageText.StartsWith("{"))
                {
                    var serverMessage = JsonSerializer.Deserialize<PlayerControllerServerMessage>(messageText);

                    if (serverMessage != null)
                    {
                        switch (serverMessage.command)
                        {
                            case PlayerControllerServerMessage.SERVER_PLAYER_STATUS:
                                // Debug only if something has changed
                                if (lastPlayerStatus == null || !lastPlayerStatus.Equals(serverMessage))
                                {
                                    Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command}) player_name={serverMessage.player_name} player_status={serverMessage.player_status} wait_queue={serverMessage.wait_queue} lives_health={serverMessage.player_lives}/{serverMessage.player_health}");

                                    // Update status for better tracking
                                    string status = serverMessage.player_status switch
                                    {
                                        0 => "waiting",
                                        1 => "setup",
                                        2 => "ready",
                                        3 => "playing",
                                        _ => serverMessage.player_status.ToString()
                                    };

                                    SetNameData($"({serverMessage.player_name}) {status} {serverMessage.player_lives}/{serverMessage.player_health} queue={serverMessage.wait_queue}");
                                }

                                if (playerStatusTimer == null)
                                {
                                    playerStatusTimer = new Stopwatch();
                                    playerStatusTimer.Start();
                                }
                                else
                                    playerStatusTimer.Restart();

                                break;

                            case PlayerControllerServerMessage.SERVER_REPORT_KILL:
                                Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command})");
                                SetNameData($"({serverMessage.player_name}) SERVER_REPORT_KILL");
                                break;

                            case PlayerControllerServerMessage.SERVER_ASK_DISCONNECT:
                                Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command})");
                                SetNameData($"({serverMessage.player_name}) SERVER_ASK_DISCONNECT");
                                serverRequestedDisconnect = true;
                                break;
                        }

                        lastPlayerStatus = serverMessage;
                    }
                    else
                    {
                        Console.WriteLine("PMClient: malformed server message");
                    }
                }
                else
                {
                    // Handle other message types (ping, etc.)
                    Console.WriteLine($"PMClient: Received non-JSON message: {messageText}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Message parsing error: {ex.Message}");
            }
        }

        private void SetNameData(string msg)
        {
            // In console version, just log the status
            Console.WriteLine($"Status: {msg}");
        }

        public void StopSimulation()
        {
            stopRequested = true;
        }
    }
}