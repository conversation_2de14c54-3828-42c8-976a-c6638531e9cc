using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Tabula.PWG.MobileController.CLI
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var config = ParseCommandLineArgs(args);

            Console.WriteLine("=== S-ARGAME Mobile Controller Network Tester ===");
            Console.WriteLine($"Configuration:");
            Console.WriteLine($"  Time: {config.Time}s");
            Console.WriteLine($"  Start Delay: {config.StartDelay}s");
            Console.WriteLine($"  End Delay: {config.EndDelay}s");
            Console.WriteLine($"  Use P2P: {config.UseP2P}");
            Console.WriteLine($"  Auth Code: {config.AuthCode}");
            Console.WriteLine($"  Server: {config.ServerAddress}:{config.ServerPort}");
            Console.WriteLine();

            var simulator = new PlayerSimulator(config);

            try
            {
                await simulator.StartSimulationAsync();

                Console.WriteLine($"Simulation completed successfully.");
                await Task.Delay(TimeSpan.FromSeconds(config.EndDelay));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Simulation failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static SimulationConfig ParseCommandLineArgs(string[] args)
        {
            var config = new SimulationConfig();

            for (int i = 0; i < args.Length; i++)
            {
                switch (args[i].ToLower())
                {
                    case "--time":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float time))
                            config.Time = time;
                        i++;
                        break;
                    case "--start-delay":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float startDelay))
                            config.StartDelay = startDelay;
                        i++;
                        break;
                    case "--end-delay":
                        if (i + 1 < args.Length && float.TryParse(args[i + 1], out float endDelay))
                            config.EndDelay = endDelay;
                        i++;
                        break;
                    case "--use-p2p":
                        if (i + 1 < args.Length && bool.TryParse(args[i + 1], out bool useP2P))
                            config.UseP2P = useP2P;
                        i++;
                        break;
                    case "--auth-code":
                        if (i + 1 < args.Length)
                            config.AuthCode = args[i + 1];
                        i++;
                        break;
                    case "--server-address":
                        if (i + 1 < args.Length)
                            config.ServerAddress = args[i + 1];
                        i++;
                        break;
                    case "--server-port":
                        if (i + 1 < args.Length && int.TryParse(args[i + 1], out int port))
                            config.ServerPort = port;
                        i++;
                        break;
                }
            }

            return config;
        }
    }

    public class SimulationConfig
    {
        public float Time { get; set; } = 60f;
        public float StartDelay { get; set; } = 2f;
        public float EndDelay { get; set; } = 5f;
        public bool UseP2P { get; set; } = true;
        public string AuthCode { get; set; } = "";
        public string ServerAddress { get; set; } = "127.0.0.1";
        public int ServerPort { get; set; } = 16100;
    }

    // Unity Vector2 replacement
    [Serializable]
    public struct Vector2
    {
        public float x, y;

        public Vector2(float x, float y)
        {
            this.x = x;
            this.y = y;
        }

        public static Vector2 Zero => new Vector2(0, 0);
    }

    // Data structures from Unity ControllerData.cs
    [Serializable]
    public struct StickData
    {
        public Vector2 direction;
        public bool is_dirty;

        public void SetData(Vector2 v)
        {
            is_dirty = true;
            direction.x = v.x;
            direction.y = v.y;
        }
    }

    [Serializable]
    public struct ButtonData
    {
        public bool pressed;
        public bool is_dirty;

        public void SetData(bool b)
        {
            is_dirty = false;
            if (pressed != b)
            {
                pressed = b;
                is_dirty = true;
            }
        }
    }

    [Serializable]
    public struct TriggerData
    {
        public float value;
        public void SetData(float v) => value = v;
    }

    [Serializable]
    public struct TouchData
    {
        public bool pressed;
        public float x;
        public float y;
        public bool is_dirty;

        public void SetData(bool pressed, float x, float y)
        {
            this.pressed = pressed;
            this.x = x;
            this.y = y;
            is_dirty = true;
        }
    }

    [Serializable]
    public struct GyroData
    {
        public float heading;
        public float pitch;
        public float roll;

        public void SetData(float heading, float pitch, float roll)
        {
            this.heading = heading;
            this.pitch = pitch;
            this.roll = roll;
        }
    }

    public class ControllerData
    {
        public StickData[] Sticks = new StickData[2];
        public ButtonData[] Buttons = new ButtonData[11];
        public TriggerData[] Triggers = new TriggerData[2];
        public TouchData Touch;
        public GyroData Gyro;

        public bool IsDirty =>
            Sticks[0].is_dirty ||
            Buttons[0].is_dirty || Buttons[1].is_dirty || Buttons[2].is_dirty || Buttons[3].is_dirty ||
            Buttons[8].is_dirty || Buttons[9].is_dirty ||
            Touch.is_dirty;

        public void ResetDirty()
        {
            Sticks[0].is_dirty = false;
            for (int i = 0; i < Buttons.Length; i++)
                Buttons[i].is_dirty = false;
            Touch.is_dirty = false;
        }
    }

    // Simulated data structures
    [Serializable]
    public class SimulatedAtom
    {
        public float time;
        public ControllerData data = new ControllerData();
    }

    public class SimulatedData
    {
        public List<SimulatedAtom> items = new List<SimulatedAtom>();

        // Create default simulation data
        public static SimulatedData CreateDefault()
        {
            var data = new SimulatedData();

            // Add some basic simulated input patterns
            for (int i = 0; i < 10; i++)
            {
                var atom = new SimulatedAtom
                {
                    time = 1.0f, // 1 second intervals
                    data = new ControllerData()
                };

                // Simulate some stick movement
                atom.data.Sticks[0].SetData(new Vector2(
                    (float)Math.Sin(i * 0.5) * 0.5f,
                    (float)Math.Cos(i * 0.5) * 0.5f
                ));

                // Simulate button presses
                if (i % 3 == 0)
                    atom.data.Buttons[0].SetData(true);

                data.items.Add(atom);
            }

            return data;
        }
    }

    // Player Controller Message structures (from Unity)
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct PlayerControllerMessage
    {
        public const int Version = 13;
        public const int KCPMSG_PLAYERCONTROLLER_DATA = 150;
        public const int KCPMSG_PLAYERCONTROLLER_COMMAND = 151;
        public const int KCPMSG_PLAYERCONTROLLER_PING = 160;

        // Flags
        public const int Flags_GamePad = 1 << 0;
        public const int Flags_Touch = 1 << 1;
        public const int Flags_Default = Flags_GamePad;

        public byte version;
        public int player_id;
        public ulong seq;
        public uint flags;

        // Gamepad
        public float stick1_x, stick1_y;
        public bool stick1_button;
        public float stick2_x, stick2_y;
        public bool stick2_button;
        public int dpad;

        // Touch
        public float touch_x, touch_y;
        public bool touch_pressed;

        // Gyro
        public float gyro_heading, gyro_pitch, gyro_roll;

        public byte[] ToByteArray()
        {
            int size = Marshal.SizeOf(this);
            byte[] arr = new byte[size];
            IntPtr ptr = Marshal.AllocHGlobal(size);
            try
            {
                Marshal.StructureToPtr(this, ptr, true);
                Marshal.Copy(ptr, arr, 0, size);
            }
            finally
            {
                Marshal.FreeHGlobal(ptr);
            }
            return arr;
        }
    }

    // Server message structure
    [Serializable]
    public class PlayerControllerServerMessage
    {
        public const string SERVER_PLAYER_STATUS = "SERVER_PLAYER_STATUS";
        public const string SERVER_ASK_DISCONNECT = "SERVER_ASK_DISCONNECT";
        public const string SERVER_REPORT_KILL = "SERVER_REPORT_KILL";

        public string command = "";
        public string package_id = "";
        public string module_id = "";
        public List<string> license_features = new List<string>();
        public string player_name = "";
        public string player_avatar = "";
        public int player_avatar_index = 0;
        public int player_color = 0;
        public float player_health = 0;
        public int player_lives = 0;
        public float position_x, position_y;
        public int player_status;
        public int wait_queue = 0;

        public bool Equals(PlayerControllerServerMessage other)
        {
            if (other == null) return false;
            return command == other.command &&
                   player_name == other.player_name &&
                   player_status == other.player_status &&
                   wait_queue == other.wait_queue &&
                   player_lives == other.player_lives &&
                   player_health == other.player_health;
        }
    }

    // Authentication classes (ported from Unity LicenseActivatorLib)
    [Serializable]
    public class LicenseAuthCodeCheck_Request
    {
        public string product = "";
        public string auth_code = "";
        public bool admin = false;
    }

    [Serializable]
    public class LicenseAuthCodeCheck_Response
    {
        public int result;

        public const int Result_OK_NORMAL = 1;  // normal auth_code
        public const int Result_OK_ADMIN = 2;   // admin auth_code
        public const int Result_ERROR = -1;
    }

    // Encryption/Decryption utilities (ported from Unity Serializer)
    public static class Serializer
    {
        public static string EncryptString(string clearText, string password)
        {
            byte[] clearBytes = Encoding.UTF8.GetBytes(clearText);
            byte[] encBytes = EncryptBytes(clearBytes, password);
            return Convert.ToBase64String(encBytes);
        }

        public static string DecryptString(string cipherText, string password)
        {
            byte[] cipherBytes = Convert.FromBase64String(cipherText);
            byte[] clearBytes = DecryptBytes(cipherBytes, password);
            return Encoding.UTF8.GetString(clearBytes);
        }

        private static byte[] EncryptBytes(byte[] clearBytes, string password)
        {
            using (var aes = Aes.Create())
            {
                var key = new Rfc2898DeriveBytes(password, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
                aes.Key = key.GetBytes(32);
                aes.IV = key.GetBytes(16);

                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(clearBytes, 0, clearBytes.Length);
                    }
                    return ms.ToArray();
                }
            }
        }

        private static byte[] DecryptBytes(byte[] cipherBytes, string password)
        {
            using (var aes = Aes.Create())
            {
                var key = new Rfc2898DeriveBytes(password, new byte[] { 0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d, 0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76 });
                aes.Key = key.GetBytes(32);
                aes.IV = key.GetBytes(16);

                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(cipherBytes, 0, cipherBytes.Length);
                    }
                    return ms.ToArray();
                }
            }
        }
    }

    // Web services client (ported from Unity TabulaWebServices)
    public static class WebServicesClient
    {
        private static readonly HttpClient httpClient = new HttpClient();
        private const string password = "jasdj38JJSAl3HHkh3484f52kJNj8h48mrDsc2945";
        private const string ProductionBaseUrl = "https://webservices.tabulatouch.com/api/";
        private const string LicenseAuthCodeCheckEndpoint = "LicenseAuthCodeCheck?code=EDCKLZUBy5p2Ty6k4PvDUXp9sodQg49Xwmf3eKUsFStKAzFu5wNckA==";

        public static async Task<LicenseAuthCodeCheck_Response?> LicenseAuthCodeCheckAsync(string product, string authCode, CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new LicenseAuthCodeCheck_Request
                {
                    product = product,
                    auth_code = authCode,
                    admin = false
                };

                var requestJson = JsonSerializer.Serialize(request);
                var encryptedRequest = Serializer.EncryptString(requestJson, password);
                var content = new StringContent(encryptedRequest, Encoding.UTF8, "application/x-www-form-urlencoded");

                var url = ProductionBaseUrl + LicenseAuthCodeCheckEndpoint;
                using var response = await httpClient.PostAsync(url, content, cancellationToken);
                response.EnsureSuccessStatusCode();

                var responseText = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(responseText))
                    return null;

                var decryptedResponse = Serializer.DecryptString(responseText, password);
                return JsonSerializer.Deserialize<LicenseAuthCodeCheck_Response>(decryptedResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LicenseAuthCodeCheck error: {ex.Message}");
                return null;
            }
        }
    }

    // License activator (ported from Unity LicenseActivatorLib)
    public static class LicenseActivatorLib
    {
        public static async Task<int> CheckAuthCodeAsync(string authCode, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(authCode))
            {
                Console.WriteLine("LicenseActivatorLib: Empty authcode");
                return -1;
            }

            try
            {
                var response = await WebServicesClient.LicenseAuthCodeCheckAsync("sargame", authCode, cancellationToken);

                if (response == null)
                {
                    Console.WriteLine("LicenseActivatorLib: Null response");
                    return -2;
                }

                // Can be either (1 or 2) normal or admin auth_code
                return response.result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LicenseActivatorLib: Exception: {ex.Message}");
                return -3;
            }
        }

        // Synchronous version for compatibility
        public static int CheckAuthCode(string authCode)
        {
            var task = CheckAuthCodeAsync(authCode, CancellationToken.None);
            return task.Result;
        }
    }

    // Player join/leave result structures
    public class PlayerJoinResult
    {
        public int player_client_id;
        public int reason;
        public int player_flags;
    }

    public class PlayerLeftResult
    {
        public const int ReasonLeft_ClientLeft = 1;
        public const int ReasonLeft_DenyClientNotFound = -1;
        public const int ReasonLeft_ClientDisconnected = -2;
        public const int ReasonLeft_KeepAliveExpired = -3;

        public int reason;

        public PlayerLeftResult(int reason)
        {
            this.reason = reason;
        }
    }

    // Task result wrapper (replacement for CoroutineWaitForTaskResult)
    public class TaskResult
    {
        public bool HasTimedOut { get; set; }
        public bool TaskIsFaulted { get; set; }
        public bool TaskIsCanceled { get; set; }
        public Exception? Exception { get; set; }
    }

    // Simplified UDP client for networking
    public class SimpleUdpClient : IDisposable
    {
        private UdpClient? udpClient;
        private IPEndPoint? remoteEndPoint;
        private bool isConnected;
        private CancellationTokenSource? cancellationTokenSource;

        public bool IsConnected => isConnected;
        public Action<byte[]>? OnMessageReceived;

        public async Task<bool> ConnectAsync(string address, int port, float timeoutSeconds, CancellationToken cancellationToken = default)
        {
            try
            {
                remoteEndPoint = new IPEndPoint(IPAddress.Parse(address), port);
                udpClient = new UdpClient();

                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(TimeSpan.FromSeconds(timeoutSeconds));

                // UdpClient.Connect is synchronous, so we wrap it in Task.Run for timeout handling
                await Task.Run(() => udpClient.Connect(remoteEndPoint), timeoutCts.Token);
                isConnected = true;

                // Start receiving messages
                cancellationTokenSource = new CancellationTokenSource();
                _ = Task.Run(() => ReceiveLoop(cancellationTokenSource.Token), cancellationToken);

                Console.WriteLine($"PMClient: Connected to {address}:{port}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Connection failed: {ex.Message}");
                return false;
            }
        }

        private async Task ReceiveLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isConnected)
                {
                    var result = await udpClient.ReceiveAsync();
                    OnMessageReceived?.Invoke(result.Buffer);
                }
            }
            catch (Exception ex)
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    Console.WriteLine($"PMClient: Receive error: {ex.Message}");
                }
            }
        }

        public async Task SendAsync(byte[] data)
        {
            if (isConnected && udpClient != null)
            {
                try
                {
                    await udpClient.SendAsync(data, data.Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"PMClient: Send error: {ex.Message}");
                }
            }
        }

        public void Disconnect()
        {
            isConnected = false;
            cancellationTokenSource?.Cancel();
            udpClient?.Close();
            udpClient?.Dispose();
            Console.WriteLine("PMClient: Disconnected");
        }

        public void Dispose()
        {
            Disconnect();
            cancellationTokenSource?.Dispose();
        }
    }

    // Main PlayerSimulator class (converted from Unity MonoBehaviour)
    public class PlayerSimulator
    {
        private readonly SimulationConfig config;
        private readonly SimpleUdpClient pmClient;
        private readonly SimulatedData simData;
        private PlayerControllerMessage dataMessage;
        private PlayerJoinResult? playerJoinResult;
        private PlayerControllerServerMessage? lastPlayerStatus;
        private Stopwatch? playerStatusTimer;
        private readonly Stopwatch simulationTimer;
        private bool serverRequestedDisconnect;
        private bool stopRequested;
        private bool isConnected;

        // Configuration from Unity PlayerSimulator
        public string PLAYER_GUID { get; private set; } = "";
        public string PLAYER_NAME { get; private set; } = "";
        public int PLAYER_ID { get; private set; } = -1;
        public string AUTH_CODE => config.AuthCode;
        public string SERVER_ADDRESS => config.ServerAddress;
        public int SERVER_PORT => config.ServerPort;
        public bool UseP2P => config.UseP2P;

        // Timing and networking parameters (from Unity)
        public float CheckAuthCodeTimeout = 60;
        public float P2PConnectionTimeout = 10;
        public float ClientConnectTimeout = 10;
        public float KeepAliveInterval = 0.5f;
        public float SendDataFrequency = 30f;
        public float PingInterval = 1.0f;
        public int ServerTimeout = 5000;

        // Statistics
        public long SentPackets { get; private set; }
        public long ReceivedPackets { get; private set; }

        public PlayerSimulator(SimulationConfig config)
        {
            this.config = config;
            this.pmClient = new SimpleUdpClient();
            this.simData = SimulatedData.CreateDefault();
            this.dataMessage = new PlayerControllerMessage();
            this.simulationTimer = new Stopwatch();

            // Setup message handling
            pmClient.OnMessageReceived = OnServerMessage;
        }

        public async Task StartSimulationAsync(CancellationToken cancellationToken = default)
        {
            SetNameData("Starting simulation");

            await Task.Delay(TimeSpan.FromSeconds(config.StartDelay), cancellationToken);

            PLAYER_GUID = Guid.NewGuid().ToString();

            if (UseP2P)
            {
                Console.WriteLine($"StartClientSimulation [P2P] AuthCode:{AUTH_CODE} Guid:{PLAYER_GUID}");
                SetNameData($"P2P: start");

                // Check authentication code (exactly as in Unity)
                int checkAuthCodeResult = -1;

                Console.WriteLine($"Checking AuthCode: {AUTH_CODE}");
                SetNameData($"P2P: checking auth...");

                try
                {
                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    timeoutCts.CancelAfter(TimeSpan.FromSeconds(CheckAuthCodeTimeout));

                    checkAuthCodeResult = await LicenseActivatorLib.CheckAuthCodeAsync(AUTH_CODE, timeoutCts.Token);
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("AuthCode check timed out");
                    checkAuthCodeResult = -4; // Timeout
                }

                Console.WriteLine($"Checking AuthCode result={checkAuthCodeResult}");
                SetNameData($"P2P: auth={checkAuthCodeResult}");

                if (checkAuthCodeResult <= 0)
                {
                    SetNameData($"P2P: authcode check failed!");
                    Console.WriteLine($"Error: authcode check failed");
                    return;
                }

                // Reject admin auth codes for simulation (exactly as in Unity)
                if (checkAuthCodeResult == LicenseAuthCodeCheck_Response.Result_OK_ADMIN)
                {
                    SetNameData($"P2P: admin authcode not allowed for simulation!");
                    Console.WriteLine($"Error: admin authcode not allowed for simulation");
                    return;
                }

                // P2P connection would happen here - for now throw exception as requested
                throw new NotImplementedException("P2P networking WebRTC implementation not yet ported. Use --use-p2p false for direct connection.");
            }
            else
            {
                Console.WriteLine($"StartClientSimulation [direct] Guid:{PLAYER_GUID}");
            }

            playerStatusTimer = null;

            // Connect to server
            var connectResult = await pmClient.ConnectAsync(SERVER_ADDRESS, SERVER_PORT, ClientConnectTimeout, cancellationToken);

            if (!connectResult)
            {
                SetNameData($"Connection failed");
                Console.WriteLine("PMClient: OnConnectionFailed()");
                return;
            }

            if (!pmClient.IsConnected)
            {
                SetNameData($"Connection failed");
                Console.WriteLine("PMClient: OnConnectionFailed()");
                return;
            }

            // Simulate player join (simplified - no actual RPC call)
            await SimulatePlayerJoinAsync(cancellationToken);

            if (playerJoinResult == null || playerJoinResult.reason < 0)
            {
                SetNameData($"Join error: reason={playerJoinResult?.reason ?? -1}");
                Console.WriteLine($"PMClient: cannot join, reason={playerJoinResult?.reason ?? -1}");
                return;
            }

            PLAYER_ID = playerJoinResult.player_client_id;
            Console.WriteLine($"PMClient: Join Successful, reason={playerJoinResult.reason} player_id={playerJoinResult.player_client_id} player_flags={playerJoinResult.player_flags}");

            isConnected = true;

            // Start sending simulated data
            if (simData != null)
            {
                await SimulateInputAsync(simData, config.Time, cancellationToken);
            }

            // Disconnect
            if (isConnected)
                await DisconnectAsync(cancellationToken);

            // Cleanup
            serverRequestedDisconnect = false;
            SentPackets = 0;
            ReceivedPackets = 0;
        }

        private async Task SimulatePlayerJoinAsync(CancellationToken cancellationToken)
        {
            // Simplified player join simulation
            await Task.Delay(100, cancellationToken); // Simulate network delay

            playerJoinResult = new PlayerJoinResult
            {
                player_client_id = new Random().Next(1000, 9999),
                reason = 1, // Success
                player_flags = 0
            };
        }

        private async Task SimulateInputAsync(SimulatedData data, float maxTime, CancellationToken cancellationToken)
        {
            dataMessage.player_id = PLAYER_ID;
            dataMessage.flags = PlayerControllerMessage.Flags_GamePad;

            simulationTimer.Start();
            var endTime = TimeSpan.FromSeconds(maxTime);
            var lastPingTime = DateTime.Now;

            Console.WriteLine($"PMClient: Starting input simulation for {maxTime} seconds");

            while (simulationTimer.Elapsed < endTime)
            {
                if (cancellationToken.IsCancellationRequested || stopRequested)
                {
                    SetNameData($"StopRequested");
                    break;
                }

                foreach (var d in data.items)
                {
                    if (simulationTimer.Elapsed >= endTime || cancellationToken.IsCancellationRequested)
                        break;

                    if (stopRequested)
                    {
                        SetNameData($"StopRequested");
                        return;
                    }

                    // Apply simulated data to message
                    if (d.data != null)
                    {
                        var stickData = d.data.Sticks[0];
                        dataMessage.stick1_x = stickData.direction.x;
                        dataMessage.stick1_y = stickData.direction.y;

                        var buttonData = d.data.Buttons[0];
                        if (buttonData.pressed)
                            dataMessage.flags |= PlayerControllerMessage.Flags_GamePad;

                        var touchData = d.data.Touch;
                        dataMessage.touch_x = touchData.x;
                        dataMessage.touch_y = touchData.y;
                        dataMessage.touch_pressed = touchData.pressed;
                    }

                    // Send message to server
                    await SendDataMessage();

                    // Wait for the specified time, but check for messages to send
                    var nextItemTime = DateTime.Now.AddSeconds(d.time);
                    while (DateTime.Now < nextItemTime)
                    {
                        if (simulationTimer.Elapsed >= endTime)
                            return;

                        // Send ping if needed
                        if ((DateTime.Now - lastPingTime).TotalSeconds > PingInterval)
                        {
                            await SendPing();
                            lastPingTime = DateTime.Now;
                        }

                        // Check for server disconnect request
                        if (serverRequestedDisconnect)
                        {
                            Console.WriteLine("PMClient: Disconnecting as server requested.");
                            await DisconnectAsync(cancellationToken, force: true);
                            SetNameData($"ServerRequestedDisconnect");
                            return;
                        }

                        // Check for server timeout
                        if (playerStatusTimer != null && playerStatusTimer.ElapsedMilliseconds > ServerTimeout)
                        {
                            Console.WriteLine("PMClient: Server timeout, no status received");
                            SetNameData($"ServerTimeout");
                            return;
                        }

                        await Task.Delay(50, cancellationToken); // Small delay to prevent busy waiting
                    }
                }
            }

            Console.WriteLine($"PMClient: Input simulation completed");
        }

        private async Task SendDataMessage()
        {
            try
            {
                dataMessage.version = PlayerControllerMessage.Version;
                dataMessage.seq++;

                var payload = dataMessage.ToByteArray();
                await pmClient.SendAsync(payload);
                SentPackets++;

                // Log every 100 packets to avoid spam
                if (SentPackets % 100 == 0)
                {
                    Console.WriteLine($"PMClient: Sent {SentPackets} packets");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Send error: {ex.Message}");
            }
        }

        private async Task SendPing()
        {
            try
            {
                // Simple ping message
                var pingData = Encoding.UTF8.GetBytes("PING");
                await pmClient.SendAsync(pingData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Ping error: {ex.Message}");
            }
        }

        private bool isDisconnecting = false;
        private async Task DisconnectAsync(CancellationToken cancellationToken = default, bool force = false)
        {
            if (isDisconnecting)
                return;

            isDisconnecting = true;

            // Notify server of player leave
            PlayerLeftResult res = null;

            if (force)
            {
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_KeepAliveExpired);
            }
            else if (!serverRequestedDisconnect)
            {
                // Simulate player leave call
                await Task.Delay(100, cancellationToken);
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_ClientLeft);
                Console.WriteLine($"PMClient: NetworkPlayerLeave reason={res.reason}");
            }
            else
            {
                res = new PlayerLeftResult(PlayerLeftResult.ReasonLeft_ClientDisconnected);
            }

            Console.WriteLine($"PMClient: Player id={PLAYER_ID} left server, reason={res.reason}");

            // Low-level client disconnect
            pmClient.Disconnect();

            PLAYER_ID = -1;
            PLAYER_GUID = string.Empty;
            isConnected = false;
            isDisconnecting = false;

            switch (res.reason)
            {
                case PlayerLeftResult.ReasonLeft_ClientLeft:
                    SetNameData($"ReasonLeft_ClientLeft");
                    break;

                case PlayerLeftResult.ReasonLeft_DenyClientNotFound:
                    SetNameData($"ReasonLeft_DenyClientNotFound");
                    break;

                case PlayerLeftResult.ReasonLeft_ClientDisconnected:
                    SetNameData($"ReasonLeft_ClientDisconnected");
                    break;
            }

            Console.WriteLine("PMClient: " + (res.reason > 0 ? "OnLeaveSuccessful()" : "OnLeaveFailed()"));
        }

        private void OnServerMessage(byte[] messageData)
        {
            ReceivedPackets++;

            try
            {
                // Try to parse as JSON server message
                var messageText = Encoding.UTF8.GetString(messageData);

                if (messageText.StartsWith("{"))
                {
                    var serverMessage = JsonSerializer.Deserialize<PlayerControllerServerMessage>(messageText);

                    if (serverMessage != null)
                    {
                        switch (serverMessage.command)
                        {
                            case PlayerControllerServerMessage.SERVER_PLAYER_STATUS:
                                // Debug only if something has changed
                                if (lastPlayerStatus == null || !lastPlayerStatus.Equals(serverMessage))
                                {
                                    Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command}) player_name={serverMessage.player_name} player_status={serverMessage.player_status} wait_queue={serverMessage.wait_queue} lives_health={serverMessage.player_lives}/{serverMessage.player_health}");

                                    // Update status for better tracking
                                    string status = serverMessage.player_status switch
                                    {
                                        0 => "waiting",
                                        1 => "setup",
                                        2 => "ready",
                                        3 => "playing",
                                        _ => serverMessage.player_status.ToString()
                                    };

                                    SetNameData($"({serverMessage.player_name}) {status} {serverMessage.player_lives}/{serverMessage.player_health} queue={serverMessage.wait_queue}");
                                }

                                if (playerStatusTimer == null)
                                {
                                    playerStatusTimer = new Stopwatch();
                                    playerStatusTimer.Start();
                                }
                                else
                                    playerStatusTimer.Restart();

                                break;

                            case PlayerControllerServerMessage.SERVER_REPORT_KILL:
                                Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command})");
                                SetNameData($"({serverMessage.player_name}) SERVER_REPORT_KILL");
                                break;

                            case PlayerControllerServerMessage.SERVER_ASK_DISCONNECT:
                                Console.WriteLine($"PMClient: OnServerMessage({serverMessage.command})");
                                SetNameData($"({serverMessage.player_name}) SERVER_ASK_DISCONNECT");
                                serverRequestedDisconnect = true;
                                break;
                        }

                        lastPlayerStatus = serverMessage;
                    }
                    else
                    {
                        Console.WriteLine("PMClient: malformed server message");
                    }
                }
                else
                {
                    // Handle other message types (ping, etc.)
                    Console.WriteLine($"PMClient: Received non-JSON message: {messageText}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PMClient: Message parsing error: {ex.Message}");
            }
        }

        private void SetNameData(string msg)
        {
            // In console version, just log the status
            Console.WriteLine($"Status: {msg}");
        }

        public void StopSimulation()
        {
            stopRequested = true;
        }
    }

    // KCP Networking Layer (ported from Unity)
    namespace kcp2k
    {
        // KCP Channel enum
        public enum KcpChannel : byte
        {
            // don't react on 0x00. might help to filter out random noise.
            Reliable = 1,
            Unreliable = 2
        }

        // KCP Error codes
        public enum ErrorCode : byte
        {
            DnsResolve,       // failed to resolve a host name
            Timeout,          // ping timeout or dead link
            Congestion,       // more messages than transport / network can process
            InvalidReceive,   // recv invalid packet (possibly intentional attack)
            InvalidSend,      // user tried to send invalid data
            ConnectionClosed, // connection closed voluntarily or lost involuntarily
            Unexpected        // unexpected error / exception, requires fix.
        }

        // KCP Header types
        public enum KcpHeader : byte
        {
            Handshake = 1,
            Data = 2,
            Disconnect = 3,
            Ping = 4
        }

        // KCP State
        enum KcpState { Connected, Authenticated, Disconnected }

        // Logging utility (ported from Unity)
        public static class Log
        {
            public static Action<string> Info = Console.WriteLine;
            public static Action<string> Warning = Console.WriteLine;
            public static Action<string> Error = Console.Error.WriteLine;
        }

        // Common utilities (ported from Unity)
        public static class Common
        {
            // helper function to resolve host to IPAddress
            public static bool ResolveHostname(string hostname, out IPAddress[] addresses)
            {
                try
                {
                    // NOTE: dns lookup is blocking. this can take a second.
                    addresses = Dns.GetHostAddresses(hostname);
                    return addresses.Length >= 1;
                }
                catch (SocketException exception)
                {
                    Log.Info($"Failed to resolve host: {hostname} reason: {exception}");
                    addresses = null;
                    return false;
                }
            }

            // if connections drop under heavy load, increase to OS limit.
            // if still not enough, increase the OS limit.
            public static void ConfigureSocketBuffers(Socket socket, int recvBufferSize, int sendBufferSize)
            {
                // log initial size for comparison.
                // remember initial size for log comparison
                int initialReceive = socket.ReceiveBufferSize;
                int initialSend = socket.SendBufferSize;

                // set to configured size
                try
                {
                    socket.ReceiveBufferSize = recvBufferSize;
                    socket.SendBufferSize = sendBufferSize;
                }
                catch (SocketException)
                {
                    Log.Warning($"Kcp: failed to set Socket RecvBufSize = {recvBufferSize} SendBufSize = {sendBufferSize}");
                }

                Log.Info($"Kcp: RecvBuf = {initialReceive}=>{socket.ReceiveBufferSize} ({socket.ReceiveBufferSize / initialReceive}x) SendBuf = {initialSend}=>{socket.SendBufferSize} ({socket.SendBufferSize / initialSend}x)");
            }
        }

        // Utils class (ported from Unity)
        public static partial class Utils
        {
            // Clamp so we don't have to depend on UnityEngine
            public static int Clamp(int value, int min, int max)
            {
                if (value < min) return min;
                if (value > max) return max;
                return value;
            }

            // encode 8 bits unsigned int
            public static int Encode8u(byte[] p, int offset, byte c)
            {
                p[0 + offset] = c;
                return 1;
            }

            // decode 8 bits unsigned int
            public static int Decode8u(byte[] p, int offset, ref byte c)
            {
                c = p[0 + offset];
                return 1;
            }

            // encode 16 bits unsigned int (lsb)
            public static int Encode16U(byte[] p, int offset, ushort w)
            {
                p[0 + offset] = (byte)(w >> 0);
                p[1 + offset] = (byte)(w >> 8);
                return 2;
            }

            // decode 16 bits unsigned int (lsb)
            public static int Decode16U(byte[] p, int offset, ref ushort w)
            {
                ushort result = 0;
                result |= (ushort)(p[0 + offset] << 0);
                result |= (ushort)(p[1 + offset] << 8);
                w = result;
                return 2;
            }

            // encode 32 bits unsigned int (lsb)
            public static int Encode32U(byte[] p, int offset, uint l)
            {
                p[0 + offset] = (byte)(l >> 0);
                p[1 + offset] = (byte)(l >> 8);
                p[2 + offset] = (byte)(l >> 16);
                p[3 + offset] = (byte)(l >> 24);
                return 4;
            }

            // decode 32 bits unsigned int (lsb)
            public static int Decode32U(byte[] p, int offset, ref uint l)
            {
                uint result = 0;
                result |= (uint)(p[0 + offset] << 0);
                result |= (uint)(p[1 + offset] << 8);
                result |= (uint)(p[2 + offset] << 16);
                result |= (uint)(p[3 + offset] << 24);
                l = result;
                return 4;
            }
        }

        // KCP Configuration (ported from Unity)
        [Serializable]
        public class KcpConfig
        {
            // socket configuration
            public bool DualMode;
            public int RecvBufferSize;
            public int SendBufferSize;

            // kcp configuration
            public int Mtu;
            public bool NoDelay;
            public uint Interval;
            public int FastResend;
            public bool CongestionWindow;
            public uint SendWindowSize;
            public uint ReceiveWindowSize;
            public int Timeout;
            public uint MaxRetransmits;

            // constructor with defaults for convenience
            public KcpConfig(
                bool DualMode = true,
                int RecvBufferSize = 1024 * 1024 * 7,
                int SendBufferSize = 1024 * 1024 * 7,
                int Mtu = Kcp.MTU_DEF,
                bool NoDelay = true,
                uint Interval = 10,
                int FastResend = 0,
                bool CongestionWindow = false,
                uint SendWindowSize = Kcp.WND_SND,
                uint ReceiveWindowSize = Kcp.WND_RCV,
                int Timeout = KcpPeer.DEFAULT_TIMEOUT,
                uint MaxRetransmits = Kcp.DEADLINK)
            {
                this.DualMode = DualMode;
                this.RecvBufferSize = RecvBufferSize;
                this.SendBufferSize = SendBufferSize;
                this.Mtu = Mtu;
                this.NoDelay = NoDelay;
                this.Interval = Interval;
                this.FastResend = FastResend;
                this.CongestionWindow = CongestionWindow;
                this.SendWindowSize = SendWindowSize;
                this.ReceiveWindowSize = ReceiveWindowSize;
                this.Timeout = Timeout;
                this.MaxRetransmits = MaxRetransmits;
            }
        }

        // Core KCP implementation (ported from Unity)
        public class Kcp
        {
            // original Kcp has a define option, which is not defined by default:
            // #define FASTACK_CONSERVE
            public const int FASTACK_CONSERVE = 0;

            public const int RTO_NDL = 30;          // no delay min rto
            public const int RTO_MIN = 100;         // normal min rto
            public const int RTO_DEF = 200;
            public const int RTO_MAX = 60000;
            public const int CMD_PUSH = 81;         // cmd: push data
            public const int CMD_ACK = 82;          // cmd: ack
            public const int CMD_WASK = 83;         // cmd: window probe (ask)
            public const int CMD_WINS = 84;         // cmd: window size (tell)
            public const int ASK_SEND = 1;          // need to send CMD_WASK
            public const int ASK_TELL = 2;          // need to send CMD_WINS
            public const int WND_SND = 32;
            public const int WND_RCV = 32;
            public const int MTU_DEF = 1400;
            public const int ACK_FAST = 3;
            public const int INTERVAL = 100;
            public const int OVERHEAD = 24;
            public const int DEADLINK = 20;
            public const int THRESH_INIT = 2;
            public const int THRESH_MIN = 2;
            public const int PROBE_INIT = 7000;     // 7 secs to probe window size
            public const int PROBE_LIMIT = 120000;  // up to 120 secs to probe window
            public const int FASTACK_LIMIT = 5;     // max times to trigger fastack

            // encode / decode
            public static int Encode8u(byte[] p, int offset, byte c)
            {
                p[0 + offset] = c;
                return 1;
            }

            public static int Decode8u(byte[] p, int offset, ref byte c)
            {
                c = p[0 + offset];
                return 1;
            }

            public static int Encode16U(byte[] p, int offset, ushort w)
            {
                p[0 + offset] = (byte)(w >> 0);
                p[1 + offset] = (byte)(w >> 8);
                return 2;
            }

            public static int Decode16U(byte[] p, int offset, ref ushort w)
            {
                ushort result = 0;
                result |= (ushort)(p[0 + offset] << 0);
                result |= (ushort)(p[1 + offset] << 8);
                w = result;
                return 2;
            }

            public static int Encode32U(byte[] p, int offset, uint l)
            {
                p[0 + offset] = (byte)(l >> 0);
                p[1 + offset] = (byte)(l >> 8);
                p[2 + offset] = (byte)(l >> 16);
                p[3 + offset] = (byte)(l >> 24);
                return 4;
            }

            public static int Decode32U(byte[] p, int offset, ref uint l)
            {
                uint result = 0;
                result |= (uint)(p[0 + offset] << 0);
                result |= (uint)(p[1 + offset] << 8);
                result |= (uint)(p[2 + offset] << 16);
                result |= (uint)(p[3 + offset] << 24);
                l = result;
                return 4;
            }

            // KCP Segment
            internal class Segment
            {
                internal uint conv = 0;
                internal uint cmd = 0;
                internal uint frg = 0;
                internal uint wnd = 0;
                internal uint ts = 0;
                internal uint sn = 0;
                internal uint una = 0;
                internal uint resendts = 0;
                internal uint rto = 0;
                internal uint fastack = 0;
                internal uint xmit = 0;
                internal byte[] data;

                internal Segment(int size)
                {
                    data = new byte[size];
                }

                // encode a segment into buffer
                internal int Encode(byte[] ptr, int offset)
                {
                    int offset_ = offset;

                    offset += Encode32U(ptr, offset, conv);
                    offset += Encode8u(ptr, offset, (byte)cmd);
                    offset += Encode8u(ptr, offset, (byte)frg);
                    offset += Encode16U(ptr, offset, (ushort)wnd);
                    offset += Encode32U(ptr, offset, ts);
                    offset += Encode32U(ptr, offset, sn);
                    offset += Encode32U(ptr, offset, una);
                    offset += Encode32U(ptr, offset, (uint)data.Length);

                    return offset - offset_;
                }
            }

            // kcp members
            uint conv; uint mtu; uint mss; uint state;
            uint snd_una; uint snd_nxt; uint rcv_nxt;
            uint ts_recent; uint ts_lastack; uint ssthresh;
            uint rx_rttval; uint rx_srtt; uint rx_rto; uint rx_minrto;
            uint snd_wnd; uint rcv_wnd; uint rmt_wnd; uint cwnd; uint probe;
            uint current; uint interval; uint ts_flush; uint xmit;
            uint nodelay; uint updated;
            uint ts_probe; uint probe_wait;
            uint dead_link; uint incr;

            List<Segment> snd_queue = new List<Segment>();
            List<Segment> rcv_queue = new List<Segment>();
            List<Segment> snd_buf = new List<Segment>();
            List<Segment> rcv_buf = new List<Segment>();

            uint[] acklist;
            uint ackcount;
            uint ackblock;
            byte[] buffer;
            int fastlimit;
            readonly Action<byte[], int> output; // callback to output data

            // create a new kcp control object, 'conv' must equal in two endpoint
            // from the same connection.
            public Kcp(uint conv, Action<byte[], int> output)
            {
                this.conv = conv;
                this.output = output;
                snd_wnd = WND_SND;
                rcv_wnd = WND_RCV;
                rmt_wnd = WND_RCV;
                mtu = MTU_DEF;
                mss = mtu - OVERHEAD;
                rx_rto = RTO_DEF;
                rx_minrto = RTO_MIN;
                interval = INTERVAL;
                ts_flush = INTERVAL;
                ssthresh = THRESH_INIT;
                fastlimit = FASTACK_LIMIT;
                dead_link = DEADLINK;
                buffer = new byte[(mtu + OVERHEAD) * 3];
            }

            // Essential KCP methods used by KcpPeer
            public int Receive(byte[] buffer, int len)
            {
                if (rcv_queue.Count == 0)
                    return -1;

                if (len < 0) len = -len;

                int peeksize = PeekSize();
                if (peeksize < 0)
                    return -2;

                if (peeksize > len)
                    return -3;

                bool recover = rcv_queue.Count >= rcv_wnd;

                // merge fragment
                len = 0;
                foreach (Segment seg in rcv_queue)
                {
                    Buffer.BlockCopy(seg.data, 0, buffer, len, seg.data.Length);
                    len += seg.data.Length;

                    uint fragment = seg.frg;
                    if (fragment == 0)
                        break;
                }

                if (len > 0)
                {
                    // remove merged segments from rcv_queue
                    int removeCount = 0;
                    foreach (Segment seg in rcv_queue)
                    {
                        removeCount++;
                        if (seg.frg == 0)
                            break;
                    }
                    rcv_queue.RemoveRange(0, removeCount);
                }

                // move available data from rcv_buf -> rcv_queue
                MoveRcvData();

                // fast recover
                if (rcv_queue.Count < rcv_wnd && recover)
                {
                    // ready to send back CMD_WINS in flush
                    // tell remote my window size
                    probe |= ASK_TELL;
                }

                return len;
            }

            public int Send(byte[] buffer, int offset, int len)
            {
                if (len < 0) return -1;

                // calculate amount of fragments necessary for 'len'
                int count;
                if (len <= mss) count = 1;
                else count = (int)((len + mss - 1) / mss);

                if (count > 255) return -2;
                if (count == 0) count = 1;

                // fragment
                for (int i = 0; i < count; i++)
                {
                    int size = len > (int)mss ? (int)mss : len;
                    Segment seg = new Segment(size);
                    Buffer.BlockCopy(buffer, offset, seg.data, 0, size);
                    seg.frg = (uint)(count - i - 1);
                    snd_queue.Add(seg);
                    offset += size;
                    len -= size;
                }

                return 0;
            }

            public void Update(uint currentTimeMilliSeconds)
            {
                current = currentTimeMilliSeconds;

                if (!updated)
                {
                    updated = 1;
                    ts_flush = current;
                }

                int slap = TimeDiff(current, ts_flush);

                if (slap >= 10000 || slap < -10000)
                {
                    ts_flush = current;
                    slap = 0;
                }

                if (slap >= 0)
                {
                    ts_flush += interval;
                    if (TimeDiff(current, ts_flush) >= 0)
                        ts_flush = current + interval;
                    Flush();
                }
            }

            public int Input(byte[] data, int offset, int size)
            {
                uint prev_una = snd_una;
                uint maxack = 0;
                bool flag = false;

                if (data == null || size < OVERHEAD) return -1;

                while (true)
                {
                    uint ts = 0;
                    uint sn = 0;
                    uint len = 0;
                    uint una = 0;
                    uint conv_ = 0;

                    ushort wnd = 0;
                    byte cmd = 0;
                    byte frg = 0;

                    if (size < OVERHEAD) break;

                    offset += Decode32U(data, offset, ref conv_);
                    if (conv != conv_) return -1;

                    offset += Decode8u(data, offset, ref cmd);
                    offset += Decode8u(data, offset, ref frg);
                    offset += Decode16U(data, offset, ref wnd);
                    offset += Decode32U(data, offset, ref ts);
                    offset += Decode32U(data, offset, ref sn);
                    offset += Decode32U(data, offset, ref una);
                    offset += Decode32U(data, offset, ref len);

                    size -= OVERHEAD;

                    if (size < len || len < 0) return -2;

                    if (cmd != CMD_PUSH && cmd != CMD_ACK &&
                        cmd != CMD_WASK && cmd != CMD_WINS)
                        return -3;

                    rmt_wnd = wnd;
                    ParseUna(una);
                    ShrinkBuf();

                    if (cmd == CMD_ACK)
                    {
                        ParseAck(sn);
                        ParseFastack(sn);
                        flag = true;
                        if (TimeDiff(ts, ts_recent) >= 0)
                        {
                            ts_recent = ts;
                            UpdateAck(TimeDiff(current, ts));
                        }
                    }
                    else if (cmd == CMD_PUSH)
                    {
                        if (TimeDiff(sn, rcv_nxt + rcv_wnd) < 0)
                        {
                            AckPush(sn, ts);
                            if (TimeDiff(sn, rcv_nxt) >= 0)
                            {
                                Segment seg = new Segment((int)len);
                                seg.conv = conv_;
                                seg.cmd = cmd;
                                seg.frg = frg;
                                seg.wnd = wnd;
                                seg.ts = ts;
                                seg.sn = sn;
                                seg.una = una;

                                if (len > 0)
                                    Buffer.BlockCopy(data, offset, seg.data, 0, (int)len);

                                ParseData(seg);
                            }
                        }
                    }
                    else if (cmd == CMD_WASK)
                    {
                        // ready to send back CMD_WINS in Flush
                        // tell remote my window size
                        probe |= ASK_TELL;
                    }
                    else if (cmd == CMD_WINS)
                    {
                        // do nothing
                    }
                    else
                    {
                        return -3;
                    }

                    offset += (int)len;
                    size -= (int)len;
                }

                if (flag)
                    ParseFastack(maxack);

                if (TimeDiff(snd_una, prev_una) > 0)
                {
                    if (cwnd < rmt_wnd)
                    {
                        uint mss_ = mss;
                        if (cwnd < ssthresh)
                        {
                            cwnd++;
                            incr += mss_;
                        }
                        else
                        {
                            if (incr < mss_) incr = mss_;
                            incr += (mss_ * mss_) / incr + (mss_ / 16);
                            if ((cwnd + 1) * mss_ <= incr)
                            {
                                cwnd++;
                            }
                        }
                        if (cwnd > rmt_wnd)
                        {
                            cwnd = rmt_wnd;
                            incr = rmt_wnd * mss_;
                        }
                    }
                }

                return 0;
            }

            public void SetMtu(uint mtu)
            {
                if (mtu < 50 || mtu < OVERHEAD)
                    throw new ArgumentException("MTU must be higher than 50 and higher than OVERHEAD");

                buffer = new byte[(mtu + OVERHEAD) * 3];
                this.mtu = mtu;
                mss = mtu - OVERHEAD;
            }

            public void SetNoDelay(uint nodelay, uint interval = INTERVAL, int resend = 0, bool nocwnd = false)
            {
                this.nodelay = nodelay;
                if (nodelay != 0)
                {
                    rx_minrto = RTO_NDL;
                }
                else
                {
                    rx_minrto = RTO_MIN;
                }

                if (interval >= 0)
                {
                    if (interval > 5000) interval = 5000;
                    else if (interval < 10) interval = 10;
                    this.interval = interval;
                }

                if (resend >= 0)
                {
                    fastlimit = resend;
                }

                if (nocwnd)
                {
                    cwnd = 0;
                }
            }

            public void SetWindowSize(uint sendWindow, uint receiveWindow)
            {
                if (sendWindow > 0)
                    snd_wnd = sendWindow;

                if (receiveWindow > 0)
                    rcv_wnd = receiveWindow;
            }

            // Helper methods (simplified versions of the full implementation)
            private int PeekSize()
            {
                if (rcv_queue.Count == 0) return -1;

                Segment seg = rcv_queue[0];
                if (seg.frg == 0) return seg.data.Length;

                if (rcv_queue.Count < seg.frg + 1) return -1;

                int length = 0;
                foreach (Segment segment in rcv_queue)
                {
                    length += segment.data.Length;
                    if (segment.frg == 0) break;
                }

                return length;
            }

            private void MoveRcvData()
            {
                // Move available data from rcv_buf to rcv_queue
                while (rcv_buf.Count > 0)
                {
                    Segment seg = rcv_buf[0];
                    if (seg.sn == rcv_nxt && rcv_queue.Count < rcv_wnd)
                    {
                        rcv_buf.RemoveAt(0);
                        rcv_queue.Add(seg);
                        rcv_nxt++;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            private void Flush()
            {
                // Simplified flush implementation
                uint current_ = current;
                byte[] buffer_ = buffer;
                int change = 0;
                int lost = 0;

                if (!updated) return;

                // flush acknowledges
                int count = (int)ackcount;
                int offset = 0;
                for (int i = 0; i < count; i++)
                {
                    if (offset + OVERHEAD > mtu)
                    {
                        output(buffer_, offset);
                        offset = 0;
                    }
                    AckGet(i, ref acklist[i * 2], ref acklist[i * 2 + 1]);
                    offset += Encode32U(buffer_, offset, conv);
                    offset += Encode8u(buffer_, offset, CMD_ACK);
                    offset += Encode8u(buffer_, offset, 0);
                    offset += Encode16U(buffer_, offset, (ushort)WndUnused());
                    offset += Encode32U(buffer_, offset, acklist[i * 2 + 1]);
                    offset += Encode32U(buffer_, offset, acklist[i * 2]);
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                }
                ackcount = 0;

                if (offset > 0)
                {
                    output(buffer_, offset);
                    offset = 0;
                }

                // probe window size (if remote window size equals zero)
                if (rmt_wnd == 0)
                {
                    if (probe_wait == 0)
                    {
                        probe_wait = PROBE_INIT;
                        ts_probe = current + probe_wait;
                    }
                    else
                    {
                        if (TimeDiff(current, ts_probe) >= 0)
                        {
                            if (probe_wait < PROBE_INIT)
                                probe_wait = PROBE_INIT;
                            probe_wait += probe_wait / 2;
                            if (probe_wait > PROBE_LIMIT)
                                probe_wait = PROBE_LIMIT;
                            ts_probe = current + probe_wait;
                            probe |= ASK_SEND;
                        }
                    }
                }
                else
                {
                    ts_probe = 0;
                    probe_wait = 0;
                }

                // flush window probing commands
                if ((probe & ASK_SEND) != 0)
                {
                    offset += Encode32U(buffer_, offset, conv);
                    offset += Encode8u(buffer_, offset, CMD_WASK);
                    offset += Encode8u(buffer_, offset, 0);
                    offset += Encode16U(buffer_, offset, (ushort)WndUnused());
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                }

                if ((probe & ASK_TELL) != 0)
                {
                    offset += Encode32U(buffer_, offset, conv);
                    offset += Encode8u(buffer_, offset, CMD_WINS);
                    offset += Encode8u(buffer_, offset, 0);
                    offset += Encode16U(buffer_, offset, (ushort)WndUnused());
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                    offset += Encode32U(buffer_, offset, 0);
                }

                probe = 0;

                // calculate window size
                uint cwnd_ = Math.Min(snd_wnd, rmt_wnd);
                if (nodelay == 0) cwnd_ = Math.Min(cwnd, cwnd_);

                // move data from snd_queue to snd_buf
                while (TimeDiff(snd_nxt, snd_una + cwnd_) < 0)
                {
                    if (snd_queue.Count == 0) break;

                    Segment newseg = snd_queue[0];
                    snd_queue.RemoveAt(0);
                    newseg.conv = conv;
                    newseg.cmd = CMD_PUSH;
                    newseg.wnd = WndUnused();
                    newseg.ts = current_;
                    newseg.sn = snd_nxt++;
                    newseg.una = rcv_nxt;
                    newseg.resendts = current_;
                    newseg.rto = rx_rto;
                    newseg.fastack = 0;
                    newseg.xmit = 1;
                    snd_buf.Add(newseg);
                }

                // flush data segments
                foreach (Segment segment in snd_buf)
                {
                    bool needsend = false;
                    if (segment.xmit == 0)
                    {
                        needsend = true;
                        segment.xmit++;
                        segment.rto = rx_rto;
                        segment.resendts = current_ + segment.rto + RtoMin();
                    }
                    else if (TimeDiff(current_, segment.resendts) >= 0)
                    {
                        needsend = true;
                        segment.xmit++;
                        xmit++;
                        if (nodelay == 0)
                        {
                            segment.rto += rx_rto;
                        }
                        else
                        {
                            segment.rto += rx_rto / 2;
                        }
                        segment.resendts = current_ + segment.rto;
                        lost = 1;
                    }
                    else if (segment.fastack >= fastlimit)
                    {
                        needsend = true;
                        segment.xmit++;
                        segment.fastack = 0;
                        segment.resendts = current_ + segment.rto;
                        change++;
                    }

                    if (needsend)
                    {
                        segment.ts = current_;
                        segment.wnd = WndUnused();
                        segment.una = rcv_nxt;

                        int need = OVERHEAD + segment.data.Length;
                        if (offset + need > mtu)
                        {
                            output(buffer_, offset);
                            offset = 0;
                        }

                        offset += segment.Encode(buffer_, offset);
                        if (segment.data.Length > 0)
                        {
                            Buffer.BlockCopy(segment.data, 0, buffer_, offset, segment.data.Length);
                            offset += segment.data.Length;
                        }

                        if (segment.xmit >= dead_link)
                        {
                            state = 0xFFFFFFFF; // dead
                        }
                    }
                }

                // flash remain segments
                if (offset > 0)
                {
                    output(buffer_, offset);
                    offset = 0;
                }

                // update ssthresh
                if (change != 0)
                {
                    uint inflight = snd_nxt - snd_una;
                    ssthresh = inflight / 2;
                    if (ssthresh < THRESH_MIN)
                        ssthresh = THRESH_MIN;
                    cwnd = ssthresh + fastlimit;
                    incr = cwnd * mss;
                }

                if (lost != 0)
                {
                    ssthresh = cwnd / 2;
                    if (ssthresh < THRESH_MIN)
                        ssthresh = THRESH_MIN;
                    cwnd = 1;
                    incr = mss;
                }

                if (cwnd < 1)
                {
                    cwnd = 1;
                    incr = mss;
                }
            }

            // Additional helper methods (simplified implementations)
            private static int TimeDiff(uint later, uint earlier)
            {
                return (int)(later - earlier);
            }

            private uint WndUnused()
            {
                if (rcv_queue.Count < rcv_wnd)
                    return rcv_wnd - (uint)rcv_queue.Count;
                return 0;
            }

            private uint RtoMin()
            {
                return rx_minrto;
            }

            private void ParseUna(uint una)
            {
                int count = 0;
                foreach (Segment seg in snd_buf)
                {
                    if (TimeDiff(una, seg.sn) > 0)
                        count++;
                    else
                        break;
                }
                if (count > 0)
                    snd_buf.RemoveRange(0, count);
            }

            private void ShrinkBuf()
            {
                if (snd_buf.Count > 0)
                    snd_una = snd_buf[0].sn;
                else
                    snd_una = snd_nxt;
            }

            private void ParseAck(uint sn)
            {
                if (TimeDiff(sn, snd_una) < 0 || TimeDiff(sn, snd_nxt) >= 0)
                    return;

                foreach (Segment seg in snd_buf)
                {
                    if (sn == seg.sn)
                    {
                        snd_buf.Remove(seg);
                        break;
                    }
                    if (TimeDiff(sn, seg.sn) < 0)
                        break;
                }
            }

            private void ParseFastack(uint sn)
            {
                if (TimeDiff(sn, snd_una) < 0 || TimeDiff(sn, snd_nxt) >= 0)
                    return;

                foreach (Segment seg in snd_buf)
                {
                    if (TimeDiff(sn, seg.sn) < 0)
                        break;
                    else if (sn != seg.sn)
                        seg.fastack++;
                }
            }

            private void AckPush(uint sn, uint ts)
            {
                if (ackcount < ackblock)
                {
                    if (acklist == null)
                    {
                        ackblock = 8;
                        acklist = new uint[ackblock * 2];
                    }
                    else if (ackcount >= ackblock)
                    {
                        ackblock *= 2;
                        uint[] newlist = new uint[ackblock * 2];
                        Array.Copy(acklist, 0, newlist, 0, acklist.Length);
                        acklist = newlist;
                    }
                }

                if (ackcount < ackblock)
                {
                    acklist[ackcount * 2] = sn;
                    acklist[ackcount * 2 + 1] = ts;
                    ackcount++;
                }
            }

            private void AckGet(int p, ref uint sn, ref uint ts)
            {
                sn = acklist[p * 2];
                ts = acklist[p * 2 + 1];
            }

            private void ParseData(Segment newseg)
            {
                uint sn = newseg.sn;

                if (TimeDiff(sn, rcv_nxt + rcv_wnd) >= 0 ||
                    TimeDiff(sn, rcv_nxt) < 0)
                    return;

                InsertSegmentInRcvBuf(newseg);
                MoveRcvData();
            }

            private void InsertSegmentInRcvBuf(Segment newseg)
            {
                bool repeat = false;
                int i;

                // check if segment already exists
                for (i = rcv_buf.Count - 1; i >= 0; i--)
                {
                    Segment seg = rcv_buf[i];
                    if (seg.sn == newseg.sn)
                    {
                        repeat = true;
                        break;
                    }
                    if (TimeDiff(newseg.sn, seg.sn) > 0)
                        break;
                }

                if (!repeat)
                {
                    if (i >= 0)
                        rcv_buf.Insert(i + 1, newseg);
                    else
                        rcv_buf.Insert(0, newseg);
                }
            }

            private void UpdateAck(int rtt)
            {
                uint rto = 0;
                if (rx_srtt == 0)
                {
                    rx_srtt = (uint)rtt;
                    rx_rttval = (uint)rtt / 2;
                }
                else
                {
                    int delta = rtt - (int)rx_srtt;
                    if (delta < 0) delta = -delta;
                    rx_rttval = (3 * rx_rttval + (uint)delta) / 4;
                    rx_srtt = (7 * rx_srtt + (uint)rtt) / 8;
                    if (rx_srtt < 1) rx_srtt = 1;
                }
                rto = rx_srtt + Math.Max(interval, 4 * rx_rttval);
                rx_rto = Utils.Clamp((int)rto, (int)rx_minrto, RTO_MAX);
            }
        }
    }
}