# S-ARGAME Mobile Controller Network Tester

A .NET console application that replicates the functionality of the Unity PlayerSimulator component for standalone network testing.

## Overview

This application is a direct port of the Unity PlayerSimulator, maintaining the exact same execution flow, logging patterns, and error handling while using standard .NET async/await patterns instead of Unity coroutines.

## Features

- **Command-line network testing** - Test server connectivity without Unity
- **Identical behavior** - Same execution flow as Unity PlayerSimulator
- **Async/await patterns** - Modern .NET asynchronous programming
- **Configurable parameters** - All simulation parameters configurable via command line
- **Real-time logging** - Detailed logging matching Unity Debug.Log patterns
- **Graceful error handling** - Proper timeout and error management

## Requirements

- .NET 6.0 or later
- Network access to target server

## Building

```bash
cd CLI_Tester
dotnet build
```

## Usage

### Basic Usage
```bash
dotnet run
```

### With Parameters
```bash
dotnet run -- --time 120 --server-address ************* --server-port 16100 --use-p2p false
```

### Command Line Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `--time` | float | 60 | Duration of simulation in seconds |
| `--start-delay` | float | 2 | Delay before starting simulation in seconds |
| `--end-delay` | float | 5 | Delay after simulation ends in seconds |
| `--use-p2p` | bool | true | Whether to use P2P networking (not implemented) |
| `--auth-code` | string | "" | Authentication code for connection |
| `--server-address` | string | "127.0.0.1" | Server IP address |
| `--server-port` | int | 16100 | Server port number |

### Examples

**Local server testing:**
```bash
dotnet run -- --time 30 --use-p2p false
```

**Remote server with custom timing:**
```bash
dotnet run -- --server-address ********* --server-port 16200 --time 300 --start-delay 5 --use-p2p false
```

**Quick connectivity test:**
```bash
dotnet run -- --time 10 --start-delay 0 --end-delay 1 --use-p2p false
```

## Implementation Details

### Unity to .NET Conversions

- **MonoBehaviour** → Regular C# classes
- **Coroutines (IEnumerator)** → async Task methods
- **yield return new WaitForSecondsRealtime()** → await Task.Delay()
- **UnityUtilities.WaitForTask()** → await Task.Run()
- **Debug.Log()** → Console.WriteLine()
- **Time.time** → Stopwatch/DateTime
- **Unity networking** → System.Net.Sockets.UdpClient

### Excluded Features

- **Server discovery** - Not implemented as per requirements
- **P2P networking** - Disabled (use --use-p2p false)
- **Multi-instance spawning** - Single simulation only
- **Unity GUI components** - Console-only interface

### Network Protocol

The application uses the same network protocol as the Unity version:
- UDP-based communication
- Binary PlayerControllerMessage structure
- JSON PlayerControllerServerMessage responses
- Ping/keepalive mechanism
- Proper connection lifecycle management

## Troubleshooting

**Connection timeouts:**
- Verify server address and port
- Check firewall settings
- Ensure server is running and accepting connections

**P2P errors:**
- Use `--use-p2p false` for direct connections
- P2P networking is not implemented in console version

**Build errors:**
- Ensure .NET 6.0 or later is installed
- Run `dotnet restore` if packages are missing

## Logging

The application provides detailed logging that matches the Unity version:
- Connection status updates
- Player join/leave events
- Server message handling
- Error conditions and timeouts
- Packet statistics

All log messages maintain the same format as the Unity PlayerSimulator for consistency.
